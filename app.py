import pandas as pd
import streamlit as st
import sys
import asyncio
import os
import re
import json
import warnings
import base64
import shutil
from datetime import datetime
from dotenv import load_dotenv

# Ignorar advertencias de ResourceWarning para evitar mensajes de "unclosed transport" y "I/O operation on closed pipe"
warnings.filterwarnings("ignore", category=ResourceWarning)

from browser_use import Browser, Agent as BrowserAgent
from src.Utilities.utils import controller
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI
from src.Utilities.checklist_manager import ChecklistManager
from src.Utilities.project_manager_service import ProjectManagerService, load_test_history
from src.UI.project_manager_ui import ProjectManagerUI
from src.UI.test_history_ui import display_test_history
from src.Core.test_service import TestService
from src.Agents.agents import StoryAgent, BrowserAutomationAgent
from src.Utilities.test_executor import TestExecutor

from src.Prompts.agno_prompts_new import (
    generate_selenium_pytest_bdd,
    generate_playwright_python,
    generate_cypress_js,
    generate_robot_framework,
    generate_java_selenium,
    generate_gherkin_scenarios,
    enhance_user_story,
    generate_manual_test_cases
)

from src.Prompts.browser_prompts import (
    generate_browser_task
)
# from src.Utilities.run_self_healing_demo import run_self_healing_demo
# # Load environment variables
load_dotenv()

if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())


# Dictionary mapping framework names to their generation functions
FRAMEWORK_GENERATORS = {
    "Selenium + PyTest BDD (Python)": generate_selenium_pytest_bdd,
    "Playwright (Python)": generate_playwright_python,
    "Cypress (JavaScript)": generate_cypress_js,
    "Robot Framework": generate_robot_framework,
    "Selenium + Cucumber (Java)": generate_java_selenium
}

# Dictionary mapping framework names to their file extensions
FRAMEWORK_EXTENSIONS = {
    "Selenium + PyTest BDD (Python)": "py",
    "Playwright (Python)": "py",
    "Cypress (JavaScript)": "js",
    "Robot Framework": "robot",
    "Selenium + Cucumber (Java)": "java"
}

# Uso de la clase TestExecutor para guardar capturas de pantalla
# Esta función queda por compatibilidad con el código existente
def save_screenshots_to_files(screenshots, test_id=None, test_type="test"):
    """
    Guarda las capturas de pantalla como archivos separados y devuelve una lista de rutas.
    Utiliza la implementación de TestExecutor.

    Args:
        screenshots: Lista de capturas de pantalla en formato base64
        test_id: Identificador único para la prueba (opcional)
        test_type: Tipo de prueba (test, smoke_test, etc.)

    Returns:
        List[str]: Lista de rutas a los archivos de capturas de pantalla
    """
    # Inicializar TestExecutor
    executor = TestExecutor(api_key=os.environ.get("GOOGLE_API_KEY"))
    # Utilizar el método de TestExecutor
    return executor.save_screenshots_to_files(screenshots, test_id, test_type)

# Función para guardar y modificar el JSON de historial
# Esta función queda por compatibilidad con el código existente
def save_history_json(history, test_id, test_type="test", screenshot_paths=None):
    """
    Guarda el historial de prueba en un archivo JSON y modifica las referencias a capturas de pantalla.
    Utiliza la implementación de TestExecutor.

    Args:
        history: Objeto de historial de la prueba
        test_id: Identificador único para la prueba
        test_type: Tipo de prueba (test, smoke_test, etc.)
        screenshot_paths: Lista de rutas a los archivos de capturas de pantalla

    Returns:
        str: Ruta al archivo JSON guardado
    """
    # Inicializar TestExecutor
    executor = TestExecutor(api_key=os.environ.get("GOOGLE_API_KEY"))
    # Utilizar el método de TestExecutor
    return executor.save_history_json(history, test_id, test_type, screenshot_paths)

# Función para modificar el JSON de historial para usar referencias a archivos
def modify_history_json(history_json_path, screenshot_paths):
    """
    Modifica el archivo JSON de historial para reemplazar las capturas de pantalla base64
    con referencias a archivos.

    Args:
        history_json_path: Ruta al archivo JSON de historial
        screenshot_paths: Lista de rutas a los archivos de capturas de pantalla

    Returns:
        str: Ruta al archivo JSON modificado
    """
    try:
        # Leer el archivo JSON original
        with open(history_json_path, "r") as f:
            history_data = json.load(f)

        # Crear una copia del archivo original
        backup_path = f"{history_json_path}.backup"
        shutil.copy2(history_json_path, backup_path)

        # Contador para las capturas de pantalla
        screenshot_index = 0

        # Recorrer las acciones del modelo y reemplazar las capturas de pantalla
        for action in history_data.get("model_actions", []):
            if "screenshot" in action and screenshot_index < len(screenshot_paths):
                # Reemplazar la captura de pantalla con la ruta al archivo
                action["screenshot_path"] = screenshot_paths[screenshot_index]
                # Eliminar completamente la captura base64 para reducir el tamaño del JSON
                action.pop("screenshot", None)
                screenshot_index += 1

        # Eliminar también las capturas base64 del estado si existen
        if "state" in history_data:
            if "screenshot" in history_data["state"]:
                history_data["state"].pop("screenshot", None)

        # Guardar el archivo JSON modificado
        with open(history_json_path, "w") as f:
            json.dump(history_data, f, indent=2)

        return history_json_path

    except Exception as e:
        print(f"Error al modificar el archivo JSON de historial: {str(e)}")
        return history_json_path

# Nota: Las funciones load_test_history y display_test_history se importan desde sus respectivos módulos

# Framework descriptions
framework_descriptions = {
    "Selenium + PyTest BDD (Python)": "Popular Python testing framework combining Selenium WebDriver with PyTest BDD for behavior-driven development. Best for Python developers who want strong test organization and reporting.",
    "Playwright (Python)": "Modern, powerful browser automation framework with built-in async support and cross-browser testing capabilities. Excellent for modern web applications and complex scenarios.",
    "Cypress (JavaScript)": "Modern, JavaScript-based end-to-end testing framework with real-time reloading and automatic waiting. Perfect for front-end developers and modern web applications.",
    "Robot Framework": "Keyword-driven testing framework that uses simple, tabular syntax. Great for teams with mixed technical expertise and for creating readable test cases.",
    "Selenium + Cucumber (Java)": "Robust combination of Selenium WebDriver with Cucumber for Java, supporting BDD. Ideal for Java teams and enterprise applications."
}

def main():

    st.set_page_config(page_title="Agents QA", layout="wide", initial_sidebar_state="expanded")

    # Forzar tema oscuro
    st.markdown("""
    <script>
    const observer = new MutationObserver(function(mutationsList, observer) {
        // Look for the theme switch in the DOM
        const themeToggleButton = document.querySelector('[data-testid="stThemeToggle"]');
        if (themeToggleButton) {
            // Check if the current theme is light
            if (themeToggleButton.getAttribute('aria-pressed') === 'false') {
                // If light, click to switch to dark
                themeToggleButton.click();
            }
            observer.disconnect(); // Stop observing once the action is performed
        }
    });
    observer.observe(document, { childList: true, subtree: true });
    </script>
    """, unsafe_allow_html=True)

    # Verificar API key
    if not os.environ.get("GOOGLE_API_KEY"):
        st.error("No se encontró la API key de Google Gemini. Por favor, configura la variable de entorno GOOGLE_API_KEY.")
        return


    # No se utiliza la información del usuario en esta aplicación

    # Initialize session state for checklist if not already present
    if 'checklist_manager' not in st.session_state:
        st.session_state.checklist_manager = ChecklistManager()

    # Initialize session state for project manager if not already present
    if 'project_manager' not in st.session_state:
        st.session_state.project_manager = ProjectManagerService()

    # Initialize project manager UI
    project_manager_ui = ProjectManagerUI(st.session_state.project_manager)

    # Initialize current view if not already present
    if 'current_view' not in st.session_state:
        st.session_state.current_view = "tests"

    # Apply custom CSS with dark theme (Shadcn style)
    st.markdown("""
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

    /* General App Styling - Dark Theme */
    :root {
        --background: #09090B;
        --foreground: #FFFFFF;
        --card: #18181B;
        --card-foreground: #FFFFFF;
        --primary: #7C3AED;
        --primary-foreground: #FFFFFF;
        --muted: #27272A;
        --muted-foreground: #A1A1AA;
        --accent: #4F46E5;
        --accent-foreground: #FFFFFF;
        --destructive: #EF4444;
        --border: #27272A;
        --input: #18181B;
        --ring: #7C3AED;
        --radius: 8px;
    }

    .stApp {
        font-family: 'Inter', sans-serif;
        background-color: var(--background);
        color: var(--foreground);
        padding: 1.5rem;
    }

    /* Navigation Bar Styling */
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 2rem;
        background-color: var(--card);
        border-radius: var(--radius);
        border: 1px solid var(--border);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(124, 58, 237, 0.05), rgba(79, 70, 229, 0.05));
        z-index: 0;
    }

    .header > * {
        position: relative;
        z-index: 1;
    }

    .header-item {
        color: var(--foreground);
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        padding: 0.7rem 1.2rem;
        border-radius: var(--radius);
        text-align: center;
        transition: all 0.3s ease;
        margin: 0 0.5rem;
        border: 1px solid transparent;
    }

    .header-item:hover {
        background: rgba(124, 58, 237, 0.1);
        transform: translateY(-2px);
        border: 1px solid rgba(124, 58, 237, 0.3);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    .header-item.active {
        background-color: var(--primary);
        color: var(--primary-foreground);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    /* Button Styling */
    .stButton > button {
        background-color: var(--primary);
        color: var(--primary-foreground);
        font-size: 1.1rem;
        font-weight: 600;
        padding: 0.8rem 1.5rem;
        border-radius: var(--radius);
        border: none;
        transition: all 0.3s ease;
        width: 100%;
        min-height: 3.5rem;
        text-align: center;
        margin: 0.5rem 0;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
        position: relative;
        overflow: hidden;
        z-index: 1;
    }

    .stButton > button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, var(--primary), var(--accent));
        opacity: 0;
        z-index: -1;
        transition: opacity 0.3s ease;
    }

    .stButton > button:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 15px rgba(124, 58, 237, 0.6);
    }

    .stButton > button:hover::before {
        opacity: 1;
    }

    .stButton > button:active {
        transform: translateY(1px);
        box-shadow: 0 2px 5px rgba(124, 58, 237, 0.4);
    }

    /* Estilos específicos para botones de acción */
    .button-row .stButton > button {
        font-size: 1.2rem;
        min-height: 4rem;
        background: linear-gradient(135deg, var(--primary), var(--accent));
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .button-row .stButton > button:hover {
        background: linear-gradient(135deg, var(--accent), var(--primary));
        transform: translateY(-4px);
        box-shadow: 0 8px 20px rgba(124, 58, 237, 0.7);
    }

    /* Button container styling */
    .button-container {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        margin: 2rem 0;
        padding: 1.5rem;
        background-color: var(--card);
        border-radius: var(--radius);
        border: 1px solid var(--border);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        position: relative;
        overflow: hidden;
    }

    .button-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(124, 58, 237, 0.05), rgba(79, 70, 229, 0.05));
        z-index: 0;
    }

    .button-container > * {
        position: relative;
        z-index: 1;
    }

    .button-row {
        display: flex;
        gap: 1.5rem;
        margin-bottom: 0.8rem;
    }

    /* Checklist Styling */
    .checklist-container {
        background-color: var(--card);
        border-radius: var(--radius);
        padding: 1rem;
        border: 1px solid var(--border);
        margin-bottom: 1.5rem;
    }

    .checklist-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        border-radius: var(--radius);
        margin-bottom: 0.5rem;
        background-color: var(--muted);
        transition: background-color 0.2s ease;
    }

    .checklist-item:hover {
        background-color: rgba(39, 39, 42, 0.8);
    }

    .checklist-item.completed {
        background-color: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.2);
    }

    .checklist-item.active {
        background-color: rgba(124, 58, 237, 0.1);
        border: 1px solid rgba(124, 58, 237, 0.3);
    }

    .checklist-item.disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .checklist-checkbox {
        margin-right: 0.75rem;
    }

    .checklist-title {
        font-weight: 500;
        flex-grow: 1;
    }

    .checklist-description {
        font-size: 0.85rem;
        color: var(--muted-foreground);
        margin-top: 0.25rem;
    }

    /* Input Fields Styling */
    .stTextInput > div > div > input,
    .stTextArea > div > div > textarea {
        background-color: var(--input);
        border: 1px solid var(--border);
        color: var(--foreground);
        border-radius: var(--radius);
        padding: 0.6rem;
        transition: border 0.2s ease, box-shadow 0.2s ease;
    }

    .stTextInput > div > div > input:focus,
    .stTextArea > div > div > textarea:focus {
        border-color: var(--ring);
        box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
    }

    /* Form Controls Styling */
    .stRadio > div {
        background-color: var(--card);
        padding: 1rem;
        border-radius: var(--radius);
        border: 1px solid var(--border);
    }

    .stRadio > div:hover {
        background-color: var(--muted);
    }

    /* Grid Layout Styling */
    .stContainer {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    /* Footer Styling */
    .footer {
        text-align: center;
        padding: 1.5rem 0;
        margin-top: 3rem;
        border-top: 1px solid var(--border);
        color: var(--muted-foreground);
        font-size: 0.9rem;
    }

    /* Tabs Styling */
    .stTabs [data-baseweb="tab-list"] {
        gap: 1rem;
        background-color: var(--muted);
        padding: 0.5rem;
        border-radius: var(--radius);
    }

    .stTabs [data-baseweb="tab"] {
        background-color: transparent;
        color: var(--muted-foreground);
        border-radius: var(--radius);
        padding: 0.5rem 1rem;
    }

    .stTabs [data-baseweb="tab"][aria-selected="true"] {
        background-color: var(--primary);
        color: var(--primary-foreground);
    }

    .main-title {
        text-align: center;
        font-family: 'Inter', sans-serif;
        font-size: 2.8rem;
        font-weight: 800;
        background: linear-gradient(to right, #7c3aed, #4f46e5, #8b5cf6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        color: transparent;
        padding: 1rem 0;
        margin-bottom: 1rem;
        letter-spacing: -0.025em;
        width: 100%;
        box-sizing: border-box;
        text-shadow: 0 0 30px rgba(124, 58, 237, 0.5);
        position: relative;
    }

    .main-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 3px;
        background: linear-gradient(to right, #7c3aed, #4f46e5);
        border-radius: 3px;
    }

    .subtitle {
        font-family: 'Inter', sans-serif;
        font-size: 1.2rem;
        color: var(--muted-foreground);
        text-align: center;
        margin-bottom: 2.5rem;
        font-weight: 400;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;
    }

    .card {
        background-color: var(--card);
        border-radius: var(--radius);
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
        border: 1px solid var(--border);
        margin-bottom: 1.5rem;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3);
    }

    .code-container {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: var(--radius);
        padding: 1.25rem;
        border: 1px solid var(--border);
        margin-top: 1.25rem;
    }

    .glow-text {
        color: var(--primary);
        font-weight: 600;
    }

    .sidebar-heading {
        background-color: var(--card);
        padding: 0.75rem;
        border-radius: var(--radius);
        text-align: center;
        font-weight: 600;
        border: 1px solid var(--border);
        margin-bottom: 1rem;
        color: var(--foreground);
    }

    .status-success {
        background-color: rgba(16, 185, 129, 0.1);
        color: #10B981; /* Green text */
        padding: 0.75rem 1rem;
        border-radius: var(--radius);
        font-weight: 500;
        border: 1px solid rgba(16, 185, 129, 0.2);
        text-align: center;
        margin: 1rem 0;
    }

    .status-error {
        background-color: rgba(239, 68, 68, 0.1);
        color: #EF4444; /* Red text */
        padding: 0.75rem 1rem;
        border-radius: var(--radius);
        font-weight: 500;
        border: 1px solid rgba(239, 68, 68, 0.2);
        text-align: center;
        margin: 1rem 0;
    }

    .tab-container {
        background-color: var(--card);
        border-radius: var(--radius);
        padding: 1.25rem;
        margin-top: 1.25rem;
        border: 1px solid var(--border);
    }

    .download-btn {
        background-color: var(--primary);
        color: var(--primary-foreground);
        text-align: center;
        padding: 0.75rem 1.25rem;
        border-radius: 9999px;
        font-weight: 500;
        display: block;
        margin: 1.25rem auto;
        width: fit-content;
        transition: all 0.2s ease;
    }

    .download-btn:hover {
        background-color: var(--accent);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
    }

    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(5px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Spinner styling */
    .stSpinner > div > div {
        border-color: var(--primary) var(--primary) transparent !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Custom Header with navigation
    st.markdown(f'''
    <div class="header fade-in">
        <div class="logo-container">
            <span class="header-item">Automatización de Pruebas con IA</span>
        </div>

    </div>
    ''', unsafe_allow_html=True)

    # Contenedor para ocultar los botones de navegación
    with st.container():
        col1, col2, col3 = st.columns([1, 1, 1])
        with col1:
            if st.button("Pruebas", key="test_mode_btn", use_container_width=True):
                st.session_state.current_view = "tests"
                st.rerun()
        with col2:
            if st.button("Historial de Tests", key="test_history_btn", use_container_width=True):
                st.session_state.current_view = "test_history"
                st.rerun()
        with col3:
            if st.button("Gestor de Proyectos", key="project_manager_btn", use_container_width=True):
                st.session_state.current_view = "project_manager"
                st.rerun()

    # Aplicar CSS para ocultar los botones pero mantenerlos funcionales y mejorar el diseño
    st.markdown("""
    <style>
    /* Ocultar los botones de navegación pero mantenerlos funcionales */
    [data-testid="column"]:has(button[kind="secondary"]) {
        display: none;
    }

    /* Ajustes para mejorar el diseño y evitar desorden */
    .main-content {
        max-width: 1200px;
        margin: 0 auto;
    }

    /* Ajustar el tamaño de las columnas para evitar desplazamiento */
    .stColumn {
        padding: 0 10px;
    }

    /* Mejorar el espaciado entre elementos */
    .stButton, .stSelectbox, .stTextInput, .stTextArea {
        margin-bottom: 1rem;
    }

    /* Ajustar el tamaño de los botones para que sean más consistentes */
    .stButton > button {
        min-height: 2.5rem;
        font-size: 1rem;
    }

    /* Mejorar la visualización en dispositivos móviles */
    @media (max-width: 768px) {
        .button-row {
            flex-direction: column;
        }

        .stColumn {
            width: 100% !important;
        }
    }
    </style>
    """, unsafe_allow_html=True)

    # Main Title with custom styling
    st.markdown('<h1 class="main-title fade-in">Agents QA</h1>', unsafe_allow_html=True)
    st.markdown('<p class="subtitle fade-in">De Historias de Usuario a Pruebas Automatizadas con Inteligencia Artificial</p>', unsafe_allow_html=True)
    # Sidebar styling
    with st.sidebar:
        st.markdown('<div class="sidebar-heading">Agents QA</div>', unsafe_allow_html=True)

        # Modo de ejecución (Full Test o Smoke Test)
        st.markdown('<div class="sidebar-heading">Modo de Ejecución</div>', unsafe_allow_html=True)
        test_mode = st.radio(
            "Seleccionar modo:",
            ["Full Test", "Smoke Test"],
            index=0
        )

        # Información sobre la herramienta
        with st.expander("Acerca de Agents QA"):
            st.markdown("### Agents QA")
            st.write("Herramienta de automatización de pruebas impulsada por IA que transforma historias de usuario en código de automatización ejecutable.")

            st.markdown("#### Características Principales")
            st.markdown("• **Full Test**: Proceso completo desde historias de usuario hasta código de automatización")
            st.markdown("• **Smoke Test**: Ejecución directa de pruebas sin pasos intermedios")
            st.markdown("• **Múltiples Frameworks**: Soporte para varios frameworks de automatización")
            st.markdown("• **Ejecución en Navegador Real**: Pruebas ejecutadas en navegador real")

            st.markdown("#### Tecnologías")
            st.markdown("• Python")
            st.markdown("• Google Gemini")
            st.markdown("• Playwright/Selenium")
            st.markdown("• Gherkin/BDD")

            st.markdown("---")
            st.markdown("Desarrollado con ❤️ para matar a los QA")

        # Información de versión
        st.markdown(
            '<div style="margin-top: 20px; text-align: center; font-size: 0.8rem; color: var(--muted-foreground);">Versión 2.0</div>',
            unsafe_allow_html=True
        )

    # Verificar la vista actual (Pruebas, Historial de Tests o Gestor de Proyectos)
    if st.session_state.current_view == "project_manager":
        # Mostrar el gestor de proyectos
        project_manager_ui.render()

        # Verificar si se solicitó ejecutar una prueba desde el project manager
        if "run_test" in st.session_state:
            test_info = st.session_state.run_test

            # Obtener la información del caso de prueba
            test_case = st.session_state.project_manager.get_test_case(
                test_info["project_id"],
                test_info["suite_id"],
                test_info["test_id"]
            )

            if test_case:
                st.markdown('<hr>', unsafe_allow_html=True)
                st.markdown('<h3 class="glow-text">Ejecutando Smoke Test</h3>', unsafe_allow_html=True)

                # Mostrar información del caso de prueba
                st.info(f"**Ejecutando:** {test_case.name}")
                st.info(f"**URL:** {test_case.url}")
                st.info(f"**Instrucciones:** {test_case.instrucciones[:100]}...")

                # Botón para confirmar ejecución
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("Confirmar Ejecución", key="confirm_execution_btn"):
                        with st.spinner("Ejecutando prueba..."):
                            try:
                                # Inicializar el servicio de tests
                                test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

                                # Ejecutar el smoke test usando los datos del caso de prueba
                                result = test_service.run_smoke_test(
                                    instructions=test_case.instrucciones,
                                    url=test_case.url,
                                    user_story=test_case.historia_de_usuario
                                )

                                if result["success"]:
                                    # Actualizar el estado del caso de prueba
                                    st.session_state.project_manager.update_test_case_status(
                                        test_info["project_id"],
                                        test_info["suite_id"],
                                        test_info["test_id"],
                                        "Passed"
                                    )

                                    # Agregar el archivo de historial al caso de prueba
                                    if "history_path" in result:
                                        st.session_state.project_manager.add_history_to_test_case(
                                            test_info["project_id"],
                                            test_info["suite_id"],
                                            test_info["test_id"],
                                            result["history_path"]
                                        )

                                    st.success(f"✅ Prueba ejecutada exitosamente!")
                                    st.success(f"📁 Historial guardado en: {result.get('history_path', 'N/A')}")

                                    # Mostrar capturas de pantalla si están disponibles
                                    if "screenshot_paths" in result and result["screenshot_paths"]:
                                        st.success(f"📸 {len(result['screenshot_paths'])} capturas de pantalla guardadas")

                                        # Mostrar la primera captura como preview
                                        try:
                                            first_screenshot = result["screenshot_paths"][0]
                                            if os.path.exists(first_screenshot):
                                                st.image(first_screenshot, caption="Primera captura de pantalla", width=300)
                                        except Exception as e:
                                            st.warning(f"No se pudo mostrar la captura: {str(e)}")

                                else:
                                    # Actualizar el estado como fallido
                                    st.session_state.project_manager.update_test_case_status(
                                        test_info["project_id"],
                                        test_info["suite_id"],
                                        test_info["test_id"],
                                        "Failed"
                                    )
                                    st.error(f"❌ Error en la ejecución: {result.get('error', 'Error desconocido')}")

                            except Exception as e:
                                st.error(f"❌ Error durante la ejecución: {str(e)}")
                                # Actualizar el estado como fallido
                                st.session_state.project_manager.update_test_case_status(
                                    test_info["project_id"],
                                    test_info["suite_id"],
                                    test_info["test_id"],
                                    "Failed"
                                )

                        # Limpiar el estado de ejecución
                        del st.session_state.run_test
                        st.rerun()

                with col2:
                    if st.button("Cancelar", key="cancel_execution_btn"):
                        del st.session_state.run_test
                        st.rerun()
            else:
                st.error("No se pudo encontrar el caso de prueba")
                del st.session_state.run_test
                st.rerun()

    elif st.session_state.current_view == "test_history":
        # Mostrar el historial de tests
        from src.UI.test_history_ui import list_test_history
        list_test_history()
    elif st.session_state.current_view == "tests":
        # Verificar el modo seleccionado (Full Test o Smoke Test)
        if test_mode == "Full Test":
            # Modo Full Test - Proceso completo con checklist
            # Main layout with two columns: checklist and content
            col_checklist, col_content = st.columns([1, 3])

            # Checklist column
            with col_checklist:
                st.markdown('<div class="checklist-container fade-in">', unsafe_allow_html=True)
                st.markdown('<h3 class="glow-text">Progreso</h3>', unsafe_allow_html=True)

                # Get checklist items and render them
                checklist_manager = st.session_state.checklist_manager
                all_items = checklist_manager.get_all_items()
                available_items = checklist_manager.get_available_items()

                # Display progress
                progress = checklist_manager.get_progress()
                st.progress(progress / 100)
                st.markdown(f"<p style='text-align: center; color: var(--muted-foreground);'>{progress:.0f}% completado</p>", unsafe_allow_html=True)

                # Display checklist items with auto-refresh
                for item in all_items:
                    # Determine item class based on state
                    item_class = "checklist-item"
                    if item.completed:
                        item_class += " completed"
                    elif item.id in [avail.id for avail in available_items]:
                        item_class += " active"
                    else:
                        item_class += " disabled"

                    # Crear un identificador único para cada elemento del checklist
                    item_key = f"checklist_{item.id}"

                    # Mostrar el elemento con un botón para completarlo si está activo
                    if item.id in [avail.id for avail in available_items] and not item.completed:
                        st.markdown(f"""
                        <div class="{item_class}">
                            <div class="checklist-checkbox">{'✅' if item.completed else '⬜'}</div>
                            <div>
                                <div class="checklist-title">{item.title}</div>
                                <div class="checklist-description">{item.description}</div>
                            </div>
                        </div>
                        """, unsafe_allow_html=True)

                        # Añadir un botón pequeño para marcar como completado
                        if st.button(f"Marcar como completado", key=item_key):
                            checklist_manager.complete_item(item.id, {"completed_manually": True})
                            st.rerun()
                    else:
                        # Mostrar sin botón si no está activo o ya está completado
                        checkbox_icon = "✓" if item.completed else " "
                        st.markdown(f"""
                        <div class="{item_class}">
                            <div class="checklist-checkbox">{checkbox_icon}</div>
                            <div>
                                <div class="checklist-title">{item.title}</div>
                                <div class="checklist-description">{item.description}</div>
                            </div>
                        </div>
                        """, unsafe_allow_html=True)

                # Add save/load session buttons
                st.markdown("<hr style='margin: 1.5rem 0; border-color: var(--border);'>", unsafe_allow_html=True)
                col1, col2 = st.columns(2)
                with col1:
                    if st.button("Guardar Sesión"):
                        save_path = checklist_manager.save_to_file()
                        st.session_state.save_path = save_path
                        st.session_state.save_success = True
                        st.rerun()

                with col2:
                    if st.button("Cargar Sesión"):
                        st.session_state.show_load_form = True
                        st.rerun()

                # Show save success message
                if "save_success" in st.session_state and st.session_state.save_success:
                    st.markdown(f"<div class='status-success'>Sesión guardada en: {st.session_state.save_path}</div>", unsafe_allow_html=True)
                    st.session_state.save_success = False

                # Show load form
                if "show_load_form" in st.session_state and st.session_state.show_load_form:
                    load_path = st.text_input("Ruta del archivo de sesión:", placeholder="saved_sessions/checklist_YYYYMMDDHHMMSS.json", key="load_session_path")
                    if st.button("Cargar"):
                        try:
                            st.session_state.checklist_manager = ChecklistManager.load_from_file(load_path)
                            st.session_state.load_success = True
                            st.session_state.show_load_form = False
                            st.rerun()
                        except Exception as e:
                            st.error(f"Error al cargar la sesión: {str(e)}")

                # Show load success message
                if "load_success" in st.session_state and st.session_state.load_success:
                    st.markdown("<div class='status-success'>Sesión cargada correctamente</div>", unsafe_allow_html=True)
                    st.session_state.load_success = False

                st.markdown('</div>', unsafe_allow_html=True)

            # Main content area
            with col_content:
                st.markdown('<div class="card fade-in">', unsafe_allow_html=True)
                st.markdown('<h3 class="glow-text">Configuración de Prueba</h3>', unsafe_allow_html=True)

                # Campo específico para URL
                test_url = st.text_input(
                    "URL de prueba",
                    placeholder="Ejemplo: https://yflow-yoizen-qa.ysocial.net",
                    help="Ingresa la URL exacta donde se ejecutará la prueba. Esta URL se preservará exactamente como fue ingresada.",
                    key="full_test_url"
                )

                # Validación de formato de URL
                is_valid_url = True
                url_error_message = ""

                if test_url:
                    # Validar formato de URL mejorado
                    url_pattern = re.compile(
                        r'^(https?://)?' # http:// o https:// (opcional)
                        r'(' # Inicio del grupo del host
                            r'localhost|' # localhost
                            r'(\d{1,3}\.){3}\d{1,3}|' # IP (ej: ***********)
                            r'([a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?' # dominio tradicional
                        r')' # Fin del grupo del host
                        r'(:\d{1,5})?' # puerto opcional (ej: :8080)
                        r'(/[a-zA-Z0-9_\-./~%]*)*' # ruta opcional con caracteres especiales
                        r'(\?[a-zA-Z0-9_\-=&%.]*)?' # query parameters opcionales (ej: ?param=value&other=123)
                        r'(#[a-zA-Z0-9_\-]*)?$' # fragmento/anchor opcional (ej: #section)
                    )

                    if not url_pattern.match(test_url):
                        is_valid_url = False
                        url_error_message = "La URL ingresada no tiene un formato válido. Por favor, verifica e intenta nuevamente."
                        st.warning(url_error_message)
                    elif not test_url.startswith(('http://', 'https://')) and not test_url.startswith('localhost'):
                        # Añadir https:// si no está presente
                        test_url = 'https://' + test_url
                        st.info("Se ha añadido 'https://' al inicio de la URL para asegurar su correcto funcionamiento.")

                # Guardar URL en session_state para usarla en los prompts
                if test_url and is_valid_url:
                    st.session_state.test_url = test_url

                # Campo para historia de usuario
                st.markdown('<h3 class="glow-text">Ingresa Historia de Usuario</h3>', unsafe_allow_html=True)
                user_story = st.text_area(
                    "Historia de Usuario",
                    placeholder="ej., Como usuario, quiero iniciar sesión con credenciales válidas para acceder a mi cuenta.",
                    key="user_story_input",
                    height=150,
                    label_visibility="collapsed"
                )
                st.markdown('</div>', unsafe_allow_html=True)

            # Contenedor de botones con mejor diseño
            with col_content:
                st.markdown('<div class="button-container">', unsafe_allow_html=True)
                st.markdown('<h3 class="glow-text" style="text-align: center; margin-bottom: 1rem;">Acciones Disponibles</h3>', unsafe_allow_html=True)

                # Primera fila de botones (2 botones más grandes)
                st.markdown('<div class="button-row">', unsafe_allow_html=True)
                col1, col2 = st.columns(2)
                with col1:
                    enhance_story_btn = st.button("Mejorar Historia", key="enhance_story_btn", use_container_width=True)
                # User Story Enhancement Section
                if enhance_story_btn and user_story:
                    with st.spinner("Mejorando historia de usuario..."):
                        # Complete the user_story checklist item
                        checklist_manager = st.session_state.checklist_manager
                        checklist_manager.complete_item("user_story", {"user_story": user_story})

                        # Call the user story enhancement agent
                        # Usar StoryAgent para mejorar la historia de usuario
                        story_agent = StoryAgent(api_key=os.environ.get("GOOGLE_API_KEY"))
                        enhanced_user_story = story_agent.enhance_story(user_story)
                        st.session_state.enhanced_user_story = enhanced_user_story

                        # Complete the enhanced_story checklist item
                        checklist_manager.complete_item("enhanced_story", {"enhanced_story": enhanced_user_story})

                        # Mostrar opciones para guardar la historia de usuario en un proyecto
                        st.markdown('<div class="status-success fade-in">¡Historia de usuario mejorada exitosamente!</div>', unsafe_allow_html=True)

                        # Opción para guardar en un proyecto
                        st.markdown('<h4 class="glow-text">Guardar Historia de Usuario</h4>', unsafe_allow_html=True)

                        # Obtener lista de proyectos
                        projects = st.session_state.project_manager.get_all_projects()
                        project_names = [p.name for p in projects]

                        if project_names:
                            # Seleccionar proyecto existente
                            selected_project = st.selectbox(
                                "Seleccionar Proyecto",
                                options=project_names,
                                key="save_story_project_selector"
                            )

                            # Botón para guardar en proyecto existente
                            if st.button("Guardar en Proyecto Existente", key="save_story_existing_btn"):
                                # Encontrar el proyecto seleccionado
                                selected_project_obj = next((p for p in projects if p.name == selected_project), None)

                                if selected_project_obj:
                                    # Crear una nueva suite si no existe una para historias de usuario
                                    user_story_suite = None
                                    for suite_id, suite in selected_project_obj.test_suites.items():
                                        if suite.name == "Historias de Usuario":
                                            user_story_suite = suite
                                            break

                                    if not user_story_suite:
                                        user_story_suite = st.session_state.project_manager.create_test_suite(
                                            project_id=selected_project_obj.project_id,
                                            name="Historias de Usuario",
                                            description="Suite para almacenar historias de usuario"
                                        )

                                    # Crear un nuevo caso de prueba para la historia de usuario
                                    story_name = user_story.split('\n')[0][:50] if '\n' in user_story else user_story[:50]
                                    test_case = st.session_state.project_manager.create_test_case(
                                        project_id=selected_project_obj.project_id,
                                        suite_id=user_story_suite.suite_id,
                                        name=f"Historia: {story_name}",
                                        description=user_story,
                                        gherkin=enhanced_user_story
                                    )

                                    if test_case:
                                        st.success(f"Historia de usuario guardada en el proyecto '{selected_project}'")
                                    else:
                                        st.error("No se pudo guardar la historia de usuario")

                        # Opción para crear un nuevo proyecto
                        st.markdown('<h4 class="glow-text">O Crear Nuevo Proyecto</h4>', unsafe_allow_html=True)
                        new_project_name = st.text_input("Nombre del Nuevo Proyecto", key="new_project_name_for_story")

                        if st.button("Crear Proyecto y Guardar", key="create_project_for_story_btn") and new_project_name:
                            # Crear nuevo proyecto
                            new_project = st.session_state.project_manager.create_project(
                                name=new_project_name,
                                description="Proyecto creado desde historia de usuario"
                            )

                            if new_project:
                                # Crear suite para historias de usuario
                                user_story_suite = st.session_state.project_manager.create_test_suite(
                                    project_id=new_project.project_id,
                                    name="Historias de Usuario",
                                    description="Suite para almacenar historias de usuario"
                                )

                                if user_story_suite:
                                    # Crear caso de prueba para la historia
                                    story_name = user_story.split('\n')[0][:50] if '\n' in user_story else user_story[:50]
                                    test_case = st.session_state.project_manager.create_test_case(
                                        project_id=new_project.project_id,
                                        suite_id=user_story_suite.suite_id,
                                        name=f"Historia: {story_name}",
                                        description=user_story,
                                        gherkin=enhanced_user_story
                                    )

                                    if test_case:
                                        st.success(f"Proyecto '{new_project_name}' creado y historia de usuario guardada")
                                    else:
                                        st.error("No se pudo guardar la historia de usuario")
                                else:
                                    st.error("No se pudo crear la suite de pruebas")
                            else:
                                st.error("No se pudo crear el proyecto")
            with col2:
                generate_manual_btn = st.button("Generar Casos de Prueba", key="generate_manual_btn")
                # Manual Test Case Generation Section
                if generate_manual_btn:
                    if "enhanced_user_story" not in st.session_state:
                        st.markdown('<div class="status-error">Por favor, mejora la historia de usuario primero.</div>', unsafe_allow_html=True)
                    else:
                        with st.spinner("Generando casos de prueba manuales..."):
                            # Call the manual test case generation function with the enhanced user story
                            # Usar StoryAgent para generar casos de prueba manuales
                            story_agent = StoryAgent(api_key=os.environ.get("GOOGLE_API_KEY"))
                            manual_test_cases_markdown = story_agent.generate_manual_tests(st.session_state.enhanced_user_story)

                            # Parse the markdown table into a pandas DataFrame
                            lines = manual_test_cases_markdown.strip().split('\n')
                            # Find the header and separator lines
                            header_line = None
                            separator_line = None
                            for i, line in enumerate(lines):
                                if '| Test Case ID' in line:
                                    header_line = i
                                elif line.startswith('|---'):
                                    separator_line = i
                                    break # Assume the first separator after header is the correct one

                            if header_line is not None and separator_line is not None:
                                header = [h.strip() for h in lines[header_line].strip('|').split('|')]
                                data_lines = lines[separator_line + 1:]
                                data = []
                                for line in data_lines:
                                    if line.strip().startswith('|'):
                                        # Split by '|' and strip whitespace, remove first and last empty strings
                                        row_data = [cell.strip() for cell in line.strip('|').split('|')]
                                        data.append(row_data)

                                manual_test_cases_data = pd.DataFrame(data, columns=header).to_dict('records')

                                st.session_state.manual_test_cases = manual_test_cases_data
                                st.session_state.edited_manual_test_cases = manual_test_cases_data # Initialize edited state

                                # Complete the manual_tests checklist item
                                checklist_manager = st.session_state.checklist_manager
                                checklist_manager.complete_item("manual_tests", {"manual_test_cases": manual_test_cases_data})

                                st.markdown('<div class="status-success fade-in">¡Casos de prueba manuales generados exitosamente!</div>', unsafe_allow_html=True)
                            else:
                                st.markdown('<div class="status-error fade-in">Error al analizar los casos de prueba manuales de la salida del agente.</div>', unsafe_allow_html=True)
                                st.write("Salida del agente:", manual_test_cases_markdown) # Display agent output for debugging
            st.markdown('</div>', unsafe_allow_html=True)

            # Segunda fila de botones (2 botones más grandes)
            with col_content:
                st.markdown('<div class="button-row">', unsafe_allow_html=True)
                col3, col4 = st.columns(2)
                with col3:
                    generate_gherkin_btn = st.button("Generar Gherkin", key="generate_gherkin_btn", use_container_width=True)
                # Gherkin Generation Section
                if generate_gherkin_btn:
                    if "edited_manual_test_cases" not in st.session_state:
                        st.markdown('<div class="status-error">Por favor, genera casos de prueba manuales primero.</div>', unsafe_allow_html=True)
                    else:
                        with st.spinner("Generando escenarios Gherkin desde casos de prueba manuales..."):
                            # Call the Gherkin agent with the edited manual test cases
                            # Convert the list of dicts back to a readable format for the agent
                            manual_test_cases_text = ""
                            if st.session_state.edited_manual_test_cases:
                                manual_test_cases_text = pd.DataFrame(st.session_state.edited_manual_test_cases).to_markdown(index=False)

                            # Usar StoryAgent para generar escenarios Gherkin
                            story_agent = StoryAgent(api_key=os.environ.get("GOOGLE_API_KEY"))
                            generated_steps = story_agent.generate_gherkin(manual_test_cases_text) # Pass manual test cases

                            # Initialize both generated_steps and edited_steps in session state
                            st.session_state.generated_steps = generated_steps
                            st.session_state.edited_steps = generated_steps

                            # Complete the gherkin checklist item
                            checklist_manager = st.session_state.checklist_manager
                            checklist_manager.complete_item("gherkin", {"gherkin_scenarios": generated_steps})

                        st.markdown('<div class="status-success fade-in">¡Escenarios Gherkin generados exitosamente!</div>', unsafe_allow_html=True)
            with col4:
                execute_btn = st.button("Ejecutar Pruebas", key="execute_btn", use_container_width=True)
                # Test Execution Section
                if execute_btn:
                    # Verificar que la URL sea válida si se ha ingresado
                    if test_url and not is_valid_url:
                        st.error("Por favor, corrige la URL antes de ejecutar la prueba.")
                    elif "edited_steps" not in st.session_state:
                        st.markdown('<div class="status-error">Por favor, genera escenarios Gherkin primero.</div>', unsafe_allow_html=True)
                    else:
                        # Check if there are unsaved changes and warn the user
                        if "scenario_editor" in st.session_state and st.session_state.get("scenario_editor", "") != st.session_state.edited_steps:
                            st.warning("Tienes cambios sin guardar. Por favor, guarda tus cambios antes de ejecutar las pruebas.")
                        else:
                            # Asegurarse de que la URL esté incluida en los escenarios
                            if 'test_url' in st.session_state and st.session_state.test_url:
                                # Verificar si la URL ya está en los escenarios
                                if st.session_state.test_url not in st.session_state.edited_steps:
                                    # Buscar líneas que comiencen con "Given" para insertar la URL
                                    lines = st.session_state.edited_steps.split('\n')
                                    for i, line in enumerate(lines):
                                        if line.strip().startswith(('Given', 'Dado', 'Dada')):
                                            # Si no hay una URL en esta línea, añadirla
                                            if 'http' not in line:
                                                lines[i] = line.replace(line, f"{line} {st.session_state.test_url}")
                                                break
                                    st.session_state.edited_steps = '\n'.join(lines)
                                    st.info(f"Se ha añadido la URL {st.session_state.test_url} a los escenarios Gherkin.")

                            with st.spinner("Ejecutando pruebas..."):
                                # Display the scenarios that will be executed
                                with col_content:
                                    st.markdown('<div class="card code-container fade-in">', unsafe_allow_html=True)
                                    st.markdown('<h4 class="glow-text">Ejecutando Escenarios:</h4>', unsafe_allow_html=True)
                                    st.code(st.session_state.edited_steps, language="gherkin")
                                    st.markdown('</div>', unsafe_allow_html=True)

                                # Use the edited steps for execution
                                steps_to_execute = st.session_state.edited_steps

                                # Complete the browser_execution checklist item
                                checklist_manager = st.session_state.checklist_manager
                                checklist_manager.complete_item("browser_execution", {"executed_scenarios": steps_to_execute})
            st.markdown('</div>', unsafe_allow_html=True)

            # Tercera fila de botones (2 botones más grandes)
            with col_content:
                st.markdown('<div class="button-row">', unsafe_allow_html=True)
                col5, col6 = st.columns(2)
                with col5:
                    generate_code_btn = st.button("Generar Código", key="generate_code_btn", use_container_width=True)
                with col6:
                    self_healing_btn = st.button("Auto-reparación", key="self_healing_btn", use_container_width=True)
                # Self-healing section
                if self_healing_btn:
                    st.markdown('<div class="status-success fade-in">Función de auto-reparación en desarrollo.</div>', unsafe_allow_html=True)
            st.markdown('</div>', unsafe_allow_html=True)

            st.markdown('</div>', unsafe_allow_html=True)

        elif test_mode == "Smoke Test":
            # Initialize columns for smoke test view
            # Ensure this is before any use of col_content or col_sidebar if they are defined globally or in a higher scope
            # For example, if col_content and col_sidebar are meant to be the main layout columns:
            # col_content, col_sidebar = st.columns([3, 1]) # Or your desired ratio

            # If col_smoke_test is specific to this block and independent of a global col_content/col_sidebar
            col_smoke_test, _ = st.columns([1, 1]) # Corrected from [1, 0]

            # Modo Smoke Test - Proceso simplificado
            with col_smoke_test:
                st.markdown('<div class="card fade-in">', unsafe_allow_html=True)
                st.markdown('<h3 class="glow-text">Modo Smoke Test</h3>', unsafe_allow_html=True)
                st.markdown('<p>Este modo permite ejecutar pruebas directamente sin pasos intermedios.</p>', unsafe_allow_html=True)

                # Campo específico para URL
                test_url = st.text_input(
                    "URL de prueba",
                    placeholder="Ejemplo: https://yflow-yoizen-qa.ysocial.net",
                    help="Ingresa la URL exacta donde se ejecutará la prueba. Esta URL se preservará exactamente como fue ingresada.",
                    key="smoke_test_url"
                )

                # Validación de formato de URL
                is_valid_url = True
                url_error_message = ""

                if test_url:
                    # Validar formato de URL mejorado
                    url_pattern = re.compile(
                        r'^(https?://)?' # http:// o https:// (opcional)
                        r'(' # Inicio del grupo del host
                            r'localhost|' # localhost
                            r'(\d{1,3}\.){3}\d{1,3}|' # IP (ej: ***********)
                            r'([a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?' # dominio tradicional
                        r')' # Fin del grupo del host
                        r'(:\d{1,5})?' # puerto opcional (ej: :8080)
                        r'(/[a-zA-Z0-9_\-./~%]*)*' # ruta opcional con caracteres especiales
                        r'(\?[a-zA-Z0-9_\-=&%.]*)?' # query parameters opcionales (ej: ?param=value&other=123)
                        r'(#[a-zA-Z0-9_\-]*)?$' # fragmento/anchor opcional (ej: #section)
                    )

                    if not url_pattern.match(test_url):
                        is_valid_url = False
                        url_error_message = "La URL ingresada no tiene un formato válido. Por favor, verifica e intenta nuevamente."
                        st.warning(url_error_message)
                    elif not test_url.startswith(('http://', 'https://')) and not test_url.startswith('localhost'):
                        # Añadir https:// si no está presente
                        test_url = 'https://' + test_url
                        st.info("Se ha añadido 'https://' al inicio de la URL para asegurar su correcto funcionamiento.")

                # Guardar URL en session_state para usarla en los prompts
                if test_url and is_valid_url:
                    st.session_state.test_url = test_url



                # Área para ingresar instrucciones de prueba
                st.markdown('<h4 class="glow-text">Instrucciones de Prueba</h4>', unsafe_allow_html=True)
                smoke_test_input = st.text_area(
                    "Instrucciones de prueba",
                    placeholder="Ingresa las instrucciones para la prueba. Ejemplo: Iniciar sesión con usuario 'standard_user' y contraseña 'secret_sauce', y agregar el producto 'Sauce Labs Bolt T-Shirt' al carrito.",
                    height=150,
                    key="smoke_test_instructions"
                )
                # Campo para historia de usuario
                st.markdown('<h4 class="glow-text">Historia de Usuario <span style="color: #888; font-size: 0.8em; font-weight: normal;">(Opcional)</span></h4>', unsafe_allow_html=True)
                smoke_test_user_story = st.text_area(
                    "Historia de Usuario (Opcional)",
                    placeholder="Ejemplo: Como usuario, quiero iniciar sesión con mis credenciales y agregar un producto al carrito para poder comprarlo más tarde.",
                    height=100,
                    key="smoke_test_user_story",
                    help="Este campo es opcional. Puedes agregar una historia de usuario para dar más contexto a tu prueba."
                )

                # Botón para ejecutar la prueba
                smoke_test_btn = st.button("Ejecutar Smoke Test")

                # Ejecutar Smoke Test
                if smoke_test_btn and smoke_test_input:
                    # Verificar que la URL sea válida si se ha ingresado
                    if test_url and not is_valid_url:
                        st.error("Por favor, corrige la URL antes de ejecutar la prueba.")
                        return

                    with st.spinner("Ejecutando Smoke Test..."):
                        try:
                            # Guardar la historia de usuario en session_state si está disponible
                            if smoke_test_user_story:
                                st.session_state.user_story = smoke_test_user_story

                            # Inicializar el servicio de tests
                            test_service = TestService(api_key=os.environ.get("GOOGLE_API_KEY"))

                            # Crear el escenario Gherkin
                            gherkin_scenario = test_service.create_gherkin_scenario(
                                instructions=smoke_test_input,
                                url=st.session_state.test_url if 'test_url' in st.session_state else None,
                                user_story=smoke_test_user_story
                            )

                            # Guardar el escenario Gherkin en session_state para usarlo en la generación de código
                            st.session_state.gherkin_scenario = gherkin_scenario
                            st.session_state.edited_steps = gherkin_scenario

                            # Mostrar el escenario generado
                            st.markdown('<div class="card code-container fade-in">', unsafe_allow_html=True)
                            st.markdown('<h4 class="glow-text">Escenario Generado:</h4>', unsafe_allow_html=True)
                            st.code(gherkin_scenario, language="gherkin")
                            st.markdown('</div>', unsafe_allow_html=True)

                            # Ejecutar el escenario en el navegador usando TestExecutor
                            # Ejecutar de manera síncrona usando el método de TestExecutor
                            result = test_service.run_smoke_test(
                                instructions=smoke_test_input,
                                url=st.session_state.test_url if 'test_url' in st.session_state else None,
                                user_story=smoke_test_user_story
                            )

                            # Verificar si la ejecución fue exitosa
                            if not result["success"]:
                                st.error(f"Error durante la ejecución del Smoke Test: {result.get('error', 'Error desconocido')}")
                                return

                            # Extraer datos importantes
                            history = result["history"]
                            test_id = result["test_id"]
                            screenshot_paths = result["screenshot_paths"]
                            history_json_path = result["history_path"]

                            # Guardar las rutas de las capturas en session_state
                            st.session_state.screenshot_paths = screenshot_paths

                            # Guardar historial en session_state para generación de código
                            st.session_state.history = {
                                "urls": history.urls(),
                                "action_names": history.action_names(),
                                "detailed_actions": [],
                                "element_xpaths": {},
                                "extracted_content": history.extracted_content(),
                                "errors": history.errors(),
                                "model_actions": history.model_actions(),
                                "execution_date": datetime.now().strftime("%d/%m/%Y %H:%M:%S"),
                                "test_id": test_id,
                                "history_json_path": history_json_path
                            }

                            # Mostrar resultados
                            st.markdown('<div class="status-success fade-in">Smoke Test completado!</div>', unsafe_allow_html=True)

                            # Cargar y mostrar el historial de pruebas completo
                            history_json_path = st.session_state.history.get("history_json_path")
                            if history_json_path and os.path.exists(history_json_path):
                                # Cargar el historial de pruebas
                                history_data = load_test_history(history_json_path)

                                # Mostrar el historial de pruebas en la interfaz
                                if history_data:
                                    st.markdown('<h3 class="glow-text">Historial de Pruebas Detallado</h3>', unsafe_allow_html=True)
                                    display_test_history(history_data, test_id)

                            # Mostrar detalles en pestañas
                            st.markdown('<div class="tab-container fade-in">', unsafe_allow_html=True)
                            tab1, tab2, tab3 = st.tabs(["Resultados", "Detalles", "Capturas"])

                            with tab1:
                                st.markdown('<h4 class="glow-text">Resultado de la Ejecución</h4>', unsafe_allow_html=True)
                                st.json(result)

                            with tab2:
                                st.markdown('<h4 class="glow-text">Contenido Extraído</h4>', unsafe_allow_html=True)

                                # Agregar JavaScript para la funcionalidad de colapsar/expandir
                                st.markdown("""
<script>
function toggleContent(id) {
    var shortContent = document.getElementById('short_' + id);
    var fullContent = document.getElementById('full_' + id);
    var button = document.getElementById('btn_' + id);

    if (shortContent.style.display === 'none') {
        shortContent.style.display = 'block';
        fullContent.style.display = 'none';
        button.textContent = 'Mostrar más';
    } else {
        shortContent.style.display = 'none';
        fullContent.style.display = 'block';
        button.textContent = 'Mostrar menos';
    }
}
</script>
""", unsafe_allow_html=True)

                                for i, content in enumerate(history.extracted_content()):
                                    # Determinar si el contenido es largo (más de 200 caracteres)
                                    is_long_content = len(content) > 200 if content else False

                                    # Crear un ID único para este contenido
                                    content_id = f"smoke_test_content_{i}"

                                    if is_long_content:
                                        # Mostrar solo los primeros 200 caracteres inicialmente
                                        short_content = content[:200] + "..." if content else ""

                                        st.markdown(f"""
                                        <div style="margin-bottom: 10px; padding: 10px; border-radius: 8px; background-color: rgba(0,0,0,0.2); border: 1px solid var(--border);">
                                            <div id="short_{content_id}" style="font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap;">{short_content}</div>
                                            <div id="full_{content_id}" style="font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap; display: none;">{content}</div>
                                            <button onclick="toggleContent('{content_id}')" id="btn_{content_id}" style="margin-top: 5px; padding: 2px 8px; background-color: #4B5563; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">Mostrar más</button>
                                        </div>
                                        """, unsafe_allow_html=True)
                                    else:
                                        # Si el contenido no es largo, mostrarlo normalmente
                                        st.write(content)

                            with tab3:
                                st.markdown('<h4 class="glow-text">Capturas de Pantalla</h4>', unsafe_allow_html=True)

                                # Verificar si hay rutas de capturas de pantalla en session_state
                                if "screenshot_paths" in st.session_state and st.session_state.screenshot_paths:
                                    screenshot_paths = st.session_state.screenshot_paths

                                    # Crear columnas para mostrar las capturas de pantalla
                                    num_screenshots = len(screenshot_paths)
                                    cols_per_row = 2  # Número de columnas por fila

                                    # Mostrar capturas de pantalla en filas de 2 columnas
                                    for i in range(0, num_screenshots, cols_per_row):
                                        cols = st.columns(cols_per_row)
                                        for j in range(cols_per_row):
                                            if i + j < num_screenshots:
                                                with cols[j]:
                                                    screenshot_path = screenshot_paths[i + j]
                                                    try:
                                                        # Cargar y mostrar la imagen
                                                        st.markdown(f"##### Captura {i + j + 1}")
                                                        st.image(screenshot_path, caption=f"Paso {i + j + 1}", use_container_width=True)

                                                        # Añadir botón de descarga
                                                        with open(screenshot_path, "rb") as f:
                                                            image_bytes = f.read()
                                                            st.download_button(
                                                                label=f"📥 Descargar",
                                                                data=image_bytes,
                                                                file_name=os.path.basename(screenshot_path),
                                                                mime="image/png",
                                                                key=f"download_screenshot_{i+j}"
                                                            )
                                                    except Exception as e:
                                                        st.error(f"Error al cargar la imagen {screenshot_path}: {str(e)}")

                                    # Añadir botón para descargar todas las capturas
                                    st.markdown("---")
                                    if st.button("📥 Descargar todas las capturas", key="download_all_screenshots"):
                                        # Crear un archivo ZIP con todas las capturas
                                        import zipfile
                                        zip_path = os.path.join("tests", f"smoke_test_{test_id}", f"capturas.zip")
                                        with zipfile.ZipFile(zip_path, "w") as zipf:
                                            for screenshot_path in screenshot_paths:
                                                zipf.write(screenshot_path, os.path.basename(screenshot_path))

                                        # Ofrecer el archivo ZIP para descarga
                                        with open(zip_path, "rb") as f:
                                            st.download_button(
                                                label="📥 Descargar ZIP",
                                                data=f.read(),
                                                file_name=os.path.basename(zip_path),
                                                mime="application/zip",
                                                key="download_zip"
                                            )
                                else:
                                    # Buscar capturas de pantalla en las acciones del modelo (método antiguo)
                                    screenshots = []
                                    for action in history.model_actions():
                                        if "screenshot" in action:
                                            screenshots.append(action["screenshot"])

                                    if screenshots:
                                        for i, screenshot in enumerate(screenshots):
                                            st.markdown(f"##### Captura {i+1}")
                                            st.image(screenshot, caption=f"Captura durante la ejecución {i+1}", use_container_width=True)
                                    else:
                                        st.info("No se capturaron imágenes durante la ejecución.")

                            st.markdown('</div>', unsafe_allow_html=True)

                        except Exception as e:
                            st.markdown(f'<div class="status-error">Error durante la ejecución del Smoke Test: {str(e)}</div>', unsafe_allow_html=True)

                            # Mostrar selector de framework y botón para generar código
                            if "history" in st.session_state:
                                st.markdown('<h4 class="glow-text">Generar Código de Automatización</h4>', unsafe_allow_html=True)

                                # Selector de framework específico para Smoke Test
                                smoke_selected_framework = st.selectbox(
                                    "Seleccionar framework:",
                                    list(FRAMEWORK_GENERATORS.keys()),
                                    index=0,
                                    key="smoke_framework_selector"
                                )

                                # Guardar el framework seleccionado en session_state
                                st.session_state.smoke_selected_framework = smoke_selected_framework

                                smoke_generate_code = st.button("Generar Código para Smoke Test", key="smoke_generate_code_btn")

                                # Generar código si se presiona el botón
                                if smoke_generate_code:
                                    # Obtener el framework seleccionado para Smoke Test
                                    smoke_framework = st.session_state.smoke_selected_framework

                                    with st.spinner(f"Generando código de automatización para {smoke_framework}..."):
                                        try:
                                            # Obtener la función generadora adecuada
                                            generator_function = FRAMEWORK_GENERATORS[smoke_framework]

                                            # Obtener el escenario Gherkin de session_state
                                            if "gherkin_scenario" in st.session_state:
                                                gherkin_scenario = st.session_state.gherkin_scenario
                                            else:
                                                # Si no está disponible, usar edited_steps como alternativa
                                                gherkin_scenario = st.session_state.edited_steps

                                            # Generar código de automatización
                                            # Convertir el historial a un formato adecuado para la función generadora
                                            history_for_generator = {
                                                "urls": st.session_state.history.get("urls", []),
                                                "action_names": st.session_state.history.get("action_names", []),
                                                "detailed_actions": st.session_state.history.get("detailed_actions", []),
                                                "element_xpaths": st.session_state.history.get("element_xpaths", {}),
                                                "extracted_content": st.session_state.history.get("extracted_content", []),
                                                "errors": st.session_state.history.get("errors", [])
                                            }

                                            automation_code = generator_function(
                                                gherkin_scenario,
                                                history_for_generator
                                            )

                                            # Guardar en session_state
                                            st.session_state.automation_code = automation_code

                                            # Mostrar código
                                            st.markdown('<div class="card code-container fade-in">', unsafe_allow_html=True)
                                            st.markdown(f'<h3 class="glow-text">Código de Automatización ({smoke_framework})</h3>', unsafe_allow_html=True)

                                            # Usar el lenguaje apropiado para resaltado de sintaxis
                                            code_language = "python"
                                            if smoke_framework == "Cypress (JavaScript)":
                                                code_language = "javascript"
                                            elif smoke_framework == "Robot Framework":
                                                code_language = "robot"
                                            elif smoke_framework == "Selenium + Cucumber (Java)":
                                                code_language = "java"

                                            st.code(automation_code, language=code_language)

                                            # Extraer nombre de característica para nombrar archivo
                                            feature_name = "smoke_test"
                                            feature_match = re.search(r"Feature:\s*(.+?)(?:\n|$)", gherkin_scenario)
                                            if feature_match:
                                                feature_name = feature_match.group(1).strip().replace(" ", "_").lower()

                                            # Obtener extensión de archivo apropiada
                                            file_ext = FRAMEWORK_EXTENSIONS[smoke_framework]

                                            # Añadir botón de descarga
                                            st.download_button(
                                                label=f"Descargar Código",
                                                data=automation_code,
                                                file_name=f"{feature_name}_automation.{file_ext}",
                                                mime="text/plain",
                                            )

                                            st.markdown('</div>', unsafe_allow_html=True)
                                            st.markdown('<div class="status-success fade-in">¡Código de automatización generado exitosamente!</div>', unsafe_allow_html=True)

                                        except Exception as e:
                                            st.markdown(f'<div class="status-error">Error al generar código para {smoke_framework}: {str(e)}</div>', unsafe_allow_html=True)

                        except Exception as e:
                            st.markdown(f'<div class="status-error">Error al procesar las instrucciones: {str(e)}</div>', unsafe_allow_html=True)

        st.markdown('</div>', unsafe_allow_html=True)



    # Display enhanced user story if available
    if "enhanced_user_story" in st.session_state:
        # Asegurarse de que col_content esté definido
        if 'col_content' not in locals():
            # Si no está definido, crear un contenedor temporal
            temp_container = st.container()
        else:
            # Si está definido, usar la columna existente
            temp_container = col_content

        with temp_container:
            st.markdown('<div class="card code-container fade-in">', unsafe_allow_html=True)
            st.markdown('<h3 class="glow-text">Historia de Usuario Mejorada</h3>', unsafe_allow_html=True)
            st.text_area(
                "Revisa y edita la historia de usuario mejorada:",
                value=st.session_state.enhanced_user_story,
                height=300,
                key="enhanced_user_story_editor"
            )
            st.markdown('</div>', unsafe_allow_html=True)



    # Display manual test cases editor
    if "manual_test_cases" in st.session_state:
        # Asegurarse de que col_content esté definido
        if 'col_content' not in locals():
            # Si no está definido, crear un contenedor temporal
            temp_container = st.container()
        else:
            # Si está definido, usar la columna existente
            temp_container = col_content

        with temp_container:
            st.markdown('<div class="card code-container fade-in">', unsafe_allow_html=True)
            st.markdown('<h3 class="glow-text">Casos de Prueba Manuales</h3>', unsafe_allow_html=True)

            # Display editable dataframe
            edited_df = st.data_editor(
                pd.DataFrame(st.session_state.edited_manual_test_cases),
                key="manual_test_case_editor",
                num_rows="dynamic",
                use_container_width=True,
                hide_index=True
            )

            # Add a save button for manual test cases
            col1, col2 = st.columns([1, 3])
            with col1:
                if st.button("💾 Guardar Cambios", key="save_manual_changes_btn"):
                    st.session_state.edited_manual_test_cases = edited_df.to_dict('records')

                    # Update the checklist item data with the edited test cases
                    checklist_manager = st.session_state.checklist_manager
                    if checklist_manager.get_item("manual_tests").completed:
                        checklist_manager.get_item("manual_tests").data["manual_test_cases"] = edited_df.to_dict('records')

                    st.session_state.manual_changes_saved = True
                    st.rerun()

            # Display save status for manual test cases
            with col2:
                if "manual_changes_saved" in st.session_state and st.session_state.manual_changes_saved:
                    st.markdown('<div class="status-success" style="margin: 0;">¡Cambios guardados exitosamente!</div>', unsafe_allow_html=True)
                    st.session_state.manual_changes_saved = False # Reset the flag

            st.markdown('</div>', unsafe_allow_html=True)



    # Display scenarios editor (whether newly generated or from session state)
    if "edited_steps" in st.session_state and test_mode == "Full Test":
        # Solo mostrar en modo Full Test
        # Asegurarse de que col_content esté definido
        if 'col_content' not in locals():
            # Si no está definido, crear un contenedor temporal
            temp_container = st.container()
        else:
            # Si está definido, usar la columna existente
            temp_container = col_content

        with temp_container:
            st.markdown('<div class="card code-container fade-in">', unsafe_allow_html=True)
            st.markdown('<h3 class="glow-text">Escenarios Gherkin</h3>', unsafe_allow_html=True)

            # Display editable text area with the current edited steps
            edited_steps = st.text_area(
                "Edita los escenarios si es necesario:",
                value=st.session_state.edited_steps,
                height=300,
                key="scenario_editor"
            )

            # Add a save button and show status
            col1, col2 = st.columns([1, 3])
            with col1:
                if st.button("💾 Guardar Cambios", key="save_changes_btn"):
                    st.session_state.edited_steps = edited_steps

                    # Update the checklist item data with the edited Gherkin
                    checklist_manager = st.session_state.checklist_manager
                    if checklist_manager.get_item("gherkin").completed:
                        checklist_manager.get_item("gherkin").data["gherkin_scenarios"] = edited_steps

                    st.session_state.changes_saved = True
                    st.rerun()

            # Display save status
            with col2:
                if "changes_saved" in st.session_state and st.session_state.changes_saved:
                    st.markdown('<div class="status-success" style="margin: 0;">¡Cambios guardados exitosamente!</div>', unsafe_allow_html=True)
                    # Reset the flag after displaying
                    st.session_state.changes_saved = False
                elif edited_steps != st.session_state.edited_steps:
                    st.markdown('<div class="status-error" style="margin: 0; font-size: 0.9rem;">* Tienes cambios sin guardar</div>', unsafe_allow_html=True)

            st.markdown('</div>', unsafe_allow_html=True)

            # Modify the execute_test function to store more detailed information
            async def execute_test(steps: str):
                try:
                    # Generar un ID único para esta prueba
                    test_id = datetime.now().strftime("%Y%m%d%H%M%S")
                    # Lista para almacenar todas las rutas de capturas de pantalla
                    all_screenshot_paths = []

                    # Parse the Gherkin content to extract scenarios
                    scenarios = []
                    current_scenario = []
                    for line in steps.split('\n'):
                        if line.strip().startswith('Scenario:'):
                            if current_scenario:
                                scenarios.append('\n'.join(current_scenario))
                            current_scenario = [line]
                        elif current_scenario:
                            current_scenario.append(line)
                    if current_scenario:
                        scenarios.append('\n'.join(current_scenario))

                    # Execute each scenario separately
                    all_results = []
                    all_actions = []
                    all_extracted_content = []
                    element_xpath_map = {}

                    # Importar la función helper aquí para asegurar que está en el alcance
                    from src.Utilities.browser_helper import create_and_run_agent

                    for scenario in scenarios:
                        # Usar la nueva API que no requiere browser_instance
                        history = await create_and_run_agent(
                            scenario_text=scenario,
                            browser_instance=None,  # No se necesita con la nueva API
                            controller_instance=controller # controller se importa globalmente en app.py
                        )

                        # Generar un ID único para este escenario
                        scenario_test_id = f"{test_id}_scenario_{len(all_results) + 1}"

                        # Extraer capturas de pantalla
                        scenario_screenshots = []
                        for action in history.model_actions():
                            if "screenshot" in action:
                                scenario_screenshots.append(action["screenshot"])

                        # Guardar capturas de pantalla como archivos separados
                        scenario_screenshot_paths = []
                        if scenario_screenshots:
                            scenario_screenshot_paths = save_screenshots_to_files(scenario_screenshots, scenario_test_id, "test")
                            # Añadir las rutas a la lista general
                            all_screenshot_paths.extend(scenario_screenshot_paths)

                        # Guardar el historial JSON con referencias a las capturas
                        history_json_path = save_history_json(history, scenario_test_id, "test", scenario_screenshot_paths)

                        result = history.final_result()
                        if isinstance(result, str):
                            # Convert string result to JSON format
                            result = {"status": result, "details": "Execution completed"}

                        # Añadir información de capturas de pantalla al resultado
                        result["screenshot_paths"] = scenario_screenshot_paths
                        result["history_json_path"] = history_json_path

                        all_results.append(result)

                        # Log all model actions for debugging
                        st.write("Debug - Model Actions:", history.model_actions())

                        # Process model actions to extract element details
                        for i, action_data in enumerate(history.model_actions()):
                            action_name = history.action_names()[i] if i < len(history.action_names()) else "Unknown Action"

                            # Create a detail record for each action
                            action_detail = {
                                "name": action_name,
                                "index": i,
                                "element_details": {}
                            }

                            # Check if this is a get_xpath_of_element action
                            if "get_xpath_of_element" in action_data:
                                element_index = action_data["get_xpath_of_element"].get("index")
                                action_detail["element_details"]["index"] = element_index

                                # Check if the interacted_element field contains XPath information
                                if "interacted_element" in action_data and action_data["interacted_element"]:
                                    element_info = action_data["interacted_element"]

                                    # Extract XPath from the DOMHistoryElement string
                                    xpath_match = re.search(r"xpath='([^']+)'", str(element_info))
                                    if xpath_match:
                                        xpath = xpath_match.group(1)
                                        element_xpath_map[element_index] = xpath
                                        action_detail["element_details"]["xpath"] = xpath

                            # Check if this is an action on an element
                            elif any(key in action_data for key in ["input_text", "click_element", "perform_element_action"]):
                                # Find the action parameters
                                for key in ["input_text", "click_element", "perform_element_action"]:
                                    if key in action_data:
                                        action_params = action_data[key]
                                        if "index" in action_params:
                                            element_index = action_params["index"]
                                            action_detail["element_details"]["index"] = element_index

                                            # If we have already captured the XPath for this element, add it
                                            if element_index in element_xpath_map:
                                                action_detail["element_details"]["xpath"] = element_xpath_map[element_index]

                                            # Also check interacted_element
                                            if "interacted_element" in action_data and action_data["interacted_element"]:
                                                element_info = action_data["interacted_element"]
                                                xpath_match = re.search(r"xpath='([^']+)'", str(element_info))
                                                if xpath_match:
                                                    xpath = xpath_match.group(1)
                                                    element_xpath_map[element_index] = xpath
                                                    action_detail["element_details"]["xpath"] = xpath

                            all_actions.append(action_detail)

                        # Also extract from content if available
                        for content in history.extracted_content():
                            all_extracted_content.append(content)

                            # Look for XPath information in extracted content
                            if isinstance(content, str):
                                xpath_match = re.search(r"The xpath of the element is (.+)", content)
                                if xpath_match:
                                    xpath = xpath_match.group(1)
                                    # Try to match with an element index from previous actions
                                    index_match = re.search(r"element (\d+)", content)
                                    if index_match:
                                        element_index = int(index_match.group(1))
                                        element_xpath_map[element_index] = xpath

                        # Guardar las rutas de las capturas en session_state
                        st.session_state.screenshot_paths = all_screenshot_paths

                        # Save combined history to session state
                        st.session_state.history = {
                            "urls": history.urls(),
                            "action_names": history.action_names(),
                            "detailed_actions": all_actions,
                            "element_xpaths": element_xpath_map,
                            "extracted_content": all_extracted_content,
                            "errors": history.errors(),
                            "model_actions": history.model_actions(),
                            "execution_date": st.session_state.get("execution_date", "Unknown"),
                            "test_id": test_id,
                            "screenshot_paths": all_screenshot_paths
                        }

                        # Display test execution details
                        st.markdown('<div class="status-success fade-in">Test execution completed!</div>', unsafe_allow_html=True)

                        # Cargar y mostrar el historial de pruebas completo para cada escenario
                        for i, result in enumerate(all_results):
                            history_json_path = result.get("history_json_path")
                            if history_json_path and os.path.exists(history_json_path):
                                # Cargar el historial de pruebas
                                history_data = load_test_history(history_json_path)

                                # Mostrar el historial de pruebas en la interfaz
                                if history_data:
                                    st.markdown(f'<h3 class="glow-text">Historial Detallado - Escenario {i+1}</h3>', unsafe_allow_html=True)
                                    display_test_history(history_data, f"{test_id}_scenario_{i+1}")

                        # Mostrar información detallada en pestañas
                        st.markdown('<div class="tab-container fade-in">', unsafe_allow_html=True)
                        tab1, tab2, tab3, tab4, tab5 = st.tabs(["Resultados", "Acciones", "Elementos", "Detalles", "Capturas"])
                        with tab1:
                            st.markdown('<h4 class="glow-text">Resultados de Ejecución</h4>', unsafe_allow_html=True)
                            for i, result in enumerate(all_results):
                                st.markdown(f'<h4 class="glow-text">Escenario {i+1}</h4>', unsafe_allow_html=True)
                                # Filtrar las claves de screenshot para evitar JSON demasiado grande
                                filtered_result = {k: v for k, v in result.items() if k != "screenshot"}
                                st.json(filtered_result)

                        with tab2:
                            st.markdown('<h4 class="glow-text">Acciones Ejecutadas</h4>', unsafe_allow_html=True)
                            for i, action in enumerate(all_actions):
                                action_text = f"{i+1}. {action['name']}"
                                if 'element_details' in action and action['element_details']:
                                    if 'xpath' in action['element_details']:
                                        action_text += f" (XPath: {action['element_details']['xpath']})"
                                    elif 'index' in action['element_details']:
                                        action_text += f" (Índice del elemento: {action['element_details']['index']})"
                                st.write(action_text)

                        with tab3:
                            st.markdown('<h4 class="glow-text">Detalles de Elementos</h4>', unsafe_allow_html=True)
                            if element_xpath_map:
                                # Create a dataframe for better visualization
                                import pandas as pd
                                element_df = pd.DataFrame([
                                    {"Índice del Elemento": index, "XPath": xpath}
                                    for index, xpath in element_xpath_map.items()
                                ])
                                st.dataframe(element_df)
                            else:
                                st.info("No se capturaron XPaths de elementos durante la ejecución.")

                                # Display raw DOM information for debugging
                                st.markdown('<h4 class="glow-text">Información DOM</h4>', unsafe_allow_html=True)
                                for i, action_data in enumerate(history.model_actions()):
                                    if "interacted_element" in action_data and action_data["interacted_element"]:
                                        st.write(f"Acción {i}: {history.action_names()[i] if i < len(history.action_names()) else 'Desconocida'}")
                                        st.code(str(action_data["interacted_element"]))

                        with tab4:
                            st.markdown('<h4 class="glow-text">Contenido Extraído</h4>', unsafe_allow_html=True)

                            # Agregar JavaScript para la funcionalidad de colapsar/expandir
                            st.markdown("""
                            <script>
                            function toggleContent(id) {
                                var shortContent = document.getElementById('short_' + id);
                                var fullContent = document.getElementById('full_' + id);
                                var button = document.getElementById('btn_' + id);

                                if (shortContent.style.display === 'none') {
                                    shortContent.style.display = 'block';
                                    fullContent.style.display = 'none';
                                    button.textContent = 'Mostrar más';
                                } else {
                                    shortContent.style.display = 'none';
                                    fullContent.style.display = 'block';
                                    button.textContent = 'Mostrar menos';
                                }
                            }
                            </script>
                            """, unsafe_allow_html=True)

                            for i, content in enumerate(all_extracted_content):
                                # Determinar si el contenido es largo (más de 200 caracteres)
                                is_long_content = len(content) > 200 if content else False

                                # Crear un ID único para este contenido
                                content_id = f"full_test_content_{i}"

                                if is_long_content:
                                    # Mostrar solo los primeros 200 caracteres inicialmente
                                    short_content = content[:200] + "..." if content else ""

                                    st.markdown(f"""
                                    <div style="margin-bottom: 10px; padding: 10px; border-radius: 8px; background-color: rgba(0,0,0,0.2); border: 1px solid var(--border);">
                                        <div id="short_{content_id}" style="font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap;">{short_content}</div>
                                        <div id="full_{content_id}" style="font-size: 0.9rem; margin-top: 5px; white-space: pre-wrap; display: none;">{content}</div>
                                        <button onclick="toggleContent('{content_id}')" id="btn_{content_id}" style="margin-top: 5px; padding: 2px 8px; background-color: #4B5563; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 0.8rem;">Mostrar más</button>
                                    </div>
                                    """, unsafe_allow_html=True)
                                else:
                                    # Si el contenido no es largo, mostrarlo normalmente
                                    st.write(content)

                        with tab5:
                            st.markdown('<h4 class="glow-text">Capturas de Pantalla</h4>', unsafe_allow_html=True)

                            # Verificar si hay rutas de capturas de pantalla en session_state
                            if "screenshot_paths" in st.session_state and st.session_state.screenshot_paths:
                                screenshot_paths = st.session_state.screenshot_paths

                                # Crear columnas para mostrar las capturas de pantalla
                                num_screenshots = len(screenshot_paths)
                                cols_per_row = 2  # Número de columnas por fila

                                # Mostrar capturas de pantalla en filas de 2 columnas
                                for i in range(0, num_screenshots, cols_per_row):
                                    cols = st.columns(cols_per_row)
                                    for j in range(cols_per_row):
                                        if i + j < num_screenshots:
                                            with cols[j]:
                                                screenshot_path = screenshot_paths[i + j]
                                                try:
                                                    # Cargar y mostrar la imagen
                                                    st.markdown(f"##### Captura {i + j + 1}")
                                                    st.image(screenshot_path, caption=f"Paso {i + j + 1}", use_container_width=True)

                                                    # Añadir botón de descarga
                                                    with open(screenshot_path, "rb") as f:
                                                        image_bytes = f.read()
                                                        st.download_button(
                                                            label=f"📥 Descargar",
                                                            data=image_bytes,
                                                            file_name=os.path.basename(screenshot_path),
                                                            mime="image/png",
                                                            key=f"download_full_screenshot_{i+j}"
                                                        )
                                                except Exception as e:
                                                    st.error(f"Error al cargar la imagen {screenshot_path}: {str(e)}")

                                # Añadir botón para descargar todas las capturas
                                st.markdown("---")
                                if st.button("📥 Descargar todas las capturas", key="download_all_full_screenshots"):
                                    # Crear un archivo ZIP con todas las capturas
                                    import zipfile
                                    zip_path = os.path.join("tests", f"test_{test_id}", f"capturas.zip")
                                    with zipfile.ZipFile(zip_path, "w") as zipf:
                                        for screenshot_path in screenshot_paths:
                                            zipf.write(screenshot_path, os.path.basename(screenshot_path))

                                    # Ofrecer el archivo ZIP para descarga
                                    with open(zip_path, "rb") as f:
                                        st.download_button(
                                            label="📥 Descargar ZIP",
                                            data=f.read(),
                                            file_name=os.path.basename(zip_path),
                                            mime="application/zip",
                                            key="download_full_zip"
                                        )
                            else:
                                st.info("No se capturaron imágenes durante la ejecución.")

                        st.markdown('</div>', unsafe_allow_html=True)

                except Exception as e:
                    st.markdown(f'<div class="status-error">Error durante la ejecución de pruebas: {str(e)}</div>', unsafe_allow_html=True)

            # Establecer la fecha de ejecución actual
            st.session_state.execution_date = datetime.now().strftime("%d/%m/%Y %H:%M:%S")

            # Ejecutar el test con manejo adecuado de eventos asíncronos
            try:
                # Crear un nuevo bucle de eventos si es necesario
                if sys.platform == "win32":
                    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(execute_test(steps_to_execute))
                loop.close()
            except Exception as e:
                st.markdown(f'<div class="status-error">Error al ejecutar el bucle de eventos: {str(e)}</div>', unsafe_allow_html=True)
    # Code Generation Section
    # Verificar si el botón de generar código fue presionado (usando session_state para mantener el estado)
    if test_mode == "Full Test" and "generate_code_btn" in locals() and generate_code_btn:
        if "edited_steps" not in st.session_state or "history" not in st.session_state:
            st.markdown('<div class="status-error">Por favor, genera y ejecuta escenarios Gherkin primero.</div>', unsafe_allow_html=True)
        else:
            # Asegurarse de que col_content esté definido para el modo Full Test
            if 'col_content' not in locals():
                # Si no está definido, crear una columna temporal para mostrar el código
                temp_col = st.container()
            else:
                # Si está definido, usar la columna existente
                temp_col = col_content

            with st.spinner(f"Generando código de automatización para {selected_framework}..."):
                try:
                    # Get the appropriate generator function
                    generator_function = FRAMEWORK_GENERATORS[selected_framework]

                    # Generate automation code using the edited steps instead of generated_steps
                    # Convertir el historial a un formato adecuado para la función generadora
                    history_for_generator = {
                        "urls": st.session_state.history.get("urls", []),
                        "action_names": st.session_state.history.get("action_names", []),
                        "detailed_actions": st.session_state.history.get("detailed_actions", []),
                        "element_xpaths": st.session_state.history.get("element_xpaths", {}),
                        "extracted_content": st.session_state.history.get("extracted_content", []),
                        "errors": st.session_state.history.get("errors", [])
                    }

                    automation_code = generator_function(
                        st.session_state.edited_steps,  # Use edited_steps instead of generated_steps
                        history_for_generator
                    )

                    # Store in session state
                    st.session_state.automation_code = automation_code

                    # Complete the code_generation checklist item
                    checklist_manager = st.session_state.checklist_manager
                    checklist_manager.complete_item("code_generation", {
                        "framework": selected_framework,
                        "automation_code": automation_code
                    })

                    # Display code
                    with temp_col:
                        st.markdown('<div class="card code-container fade-in">', unsafe_allow_html=True)
                        st.markdown(f'<h3 class="glow-text">Código de Automatización ({selected_framework})</h3>', unsafe_allow_html=True)

                        # Use appropriate language for syntax highlighting
                        code_language = "python"
                        if selected_framework == "Cypress (JavaScript)":
                            code_language = "javascript"
                        elif selected_framework == "Robot Framework":
                            code_language = "robot"
                        elif selected_framework == "Selenium + Cucumber (Java)":
                            code_language = "java"

                        st.code(automation_code, language=code_language)
                        st.markdown('</div>', unsafe_allow_html=True)

                        # Extract feature name for file naming - use edited_steps instead of generated_steps
                        feature_name = "automated_test"
                        feature_match = re.search(r"Feature:\s*(.+?)(?:\n|$)", st.session_state.edited_steps)
                        if feature_match:
                            feature_name = feature_match.group(1).strip().replace(" ", "_").lower()

                        # Get appropriate file extension
                        file_ext = FRAMEWORK_EXTENSIONS[selected_framework]

                        # Add download button
                        col1, col2, col3 = st.columns([1, 2, 1])
                        with col2:
                            st.download_button(
                                label=f"📥 Descargar Código",
                                data=automation_code,
                                file_name=f"{feature_name}_automation.{file_ext}",
                                mime="text/plain",
                            )

                            # Add save results button
                            if st.button("💾 Guardar Resultados", key="save_results_btn"):
                                # Complete the save_results checklist item
                                checklist_manager.complete_item("save_results", {
                                    "timestamp": datetime.now().isoformat(),
                                    "framework": selected_framework,
                                    "feature_name": feature_name,
                                    "file_extension": file_ext
                                })
                                st.session_state.results_saved = True
                                st.rerun()

                            # Agregar botón para guardar como caso de prueba
                            if st.button("📋 Guardar como Caso de Prueba", key="save_as_test_case_btn"):
                                # Mostrar opciones para guardar en un proyecto
                                st.markdown('<h4 class="glow-text">Guardar como Caso de Prueba</h4>', unsafe_allow_html=True)

                                # Obtener lista de proyectos
                                projects = st.session_state.project_manager.get_all_projects()
                                project_names = [p.name for p in projects]

                                if project_names:
                                    # Seleccionar proyecto existente
                                    selected_project = st.selectbox(
                                        "Seleccionar Proyecto",
                                        options=project_names,
                                        key="save_test_project_selector"
                                    )

                                    # Encontrar el proyecto seleccionado
                                    selected_project_obj = next((p for p in projects if p.name == selected_project), None)

                                    if selected_project_obj:
                                        # Obtener suites del proyecto
                                        suites = selected_project_obj.get_all_test_suites()
                                        suite_names = [s.name for s in suites]

                                        if suite_names:
                                            # Seleccionar suite existente
                                            selected_suite = st.selectbox(
                                                "Seleccionar Suite de Pruebas",
                                                options=suite_names,
                                                key="save_test_suite_selector"
                                            )

                                            # Encontrar la suite seleccionada
                                            selected_suite_obj = next((s for s in suites if s.name == selected_suite), None)

                                            if selected_suite_obj:
                                                # Nombre para el caso de prueba
                                                test_case_name = st.text_input(
                                                    "Nombre del Caso de Prueba",
                                                    value=feature_name.replace("_", " ").title(),
                                                    key="test_case_name_input"
                                                )

                                                # Botón para guardar
                                                if st.button("Guardar Caso de Prueba", key="save_test_case_confirm_btn"):
                                                    # Crear el caso de prueba
                                                    test_case = st.session_state.project_manager.create_test_case(
                                                        project_id=selected_project_obj.project_id,
                                                        suite_id=selected_suite_obj.suite_id,
                                                        name=test_case_name,
                                                        description=f"Caso de prueba generado automáticamente para {feature_name}",
                                                        gherkin=st.session_state.edited_steps,
                                                        instrucciones="Ejecutar escenario Gherkin",
                                                        historia_de_usuario="Prueba exitosa",
                                                        url=""
                                                    )

                                                    if test_case:
                                                        # Actualizar el caso de prueba con el código generado
                                                        test_case.code = automation_code
                                                        test_case.framework = selected_framework

                                                        # Guardar el historial de ejecución si existe
                                                        if "history" in st.session_state:
                                                            # Crear una copia del historial para el caso de prueba
                                                            history_copy_path = f"test_case_history_{test_case.test_id}.json"
                                                            if "history_json_path" in st.session_state.history:
                                                                original_history_path = st.session_state.history["history_json_path"]
                                                                if os.path.exists(original_history_path):
                                                                    import shutil
                                                                    shutil.copy2(original_history_path, history_copy_path)
                                                                    test_case.history_files.append(history_copy_path)

                                                        # Guardar el proyecto actualizado
                                                        st.session_state.project_manager.save_project(selected_project_obj)

                                                        st.success(f"Caso de prueba '{test_case_name}' guardado exitosamente en la suite '{selected_suite}'")
                                                    else:
                                                        st.error("No se pudo crear el caso de prueba")
                                        else:
                                            st.warning("No hay suites de prueba en este proyecto. Crea una suite primero.")

                                            # Opción para crear una nueva suite
                                            new_suite_name = st.text_input("Nombre de la Nueva Suite", key="new_suite_name_input")

                                            if st.button("Crear Suite", key="create_suite_btn") and new_suite_name:
                                                # Crear la suite
                                                new_suite = st.session_state.project_manager.create_test_suite(
                                                    project_id=selected_project_obj.project_id,
                                                    name=new_suite_name,
                                                    description="Suite creada desde generación de código"
                                                )

                                                if new_suite:
                                                    st.success(f"Suite '{new_suite_name}' creada exitosamente. Ahora puedes guardar el caso de prueba.")
                                                    st.rerun()
                                                else:
                                                    st.error("No se pudo crear la suite de pruebas")
                                else:
                                    st.warning("No hay proyectos disponibles. Crea un proyecto primero.")

                                    # Opción para crear un nuevo proyecto
                                    new_project_name = st.text_input("Nombre del Nuevo Proyecto", key="new_project_name_input")

                                    if st.button("Crear Proyecto", key="create_project_btn") and new_project_name:
                                        # Crear el proyecto
                                        new_project = st.session_state.project_manager.create_project(
                                            name=new_project_name,
                                            description="Proyecto creado desde generación de código"
                                        )

                                        if new_project:
                                            st.success(f"Proyecto '{new_project_name}' creado exitosamente. Ahora puedes crear una suite y guardar el caso de prueba.")
                                            st.rerun()
                                        else:
                                            st.error("No se pudo crear el proyecto")

                        # Display save results status
                        if "results_saved" in st.session_state and st.session_state.results_saved:
                            st.markdown('<div class="status-success fade-in">¡Resultados guardados exitosamente!</div>', unsafe_allow_html=True)
                            st.session_state.results_saved = False

                    st.markdown('<div class="status-success fade-in">¡Código de automatización generado exitosamente!</div>', unsafe_allow_html=True)

                except Exception as e:
                    st.markdown(f'<div class="status-error">Error al generar código para {selected_framework}: {str(e)}</div>', unsafe_allow_html=True)

    # Footer
    st.markdown('<div class="footer fade-in">© 2025 Agents QA | Automatización de Pruebas con IA | Versión 2.0</div>', unsafe_allow_html=True)

if __name__ == "__main__":
    main()


## USER-STORY

# as a user i want to login into https://www.saucedemo.com/ with user name "standard_user", password "secret_sauce" and add this product to cart "Sauce Labs Bolt T-Shirt'
